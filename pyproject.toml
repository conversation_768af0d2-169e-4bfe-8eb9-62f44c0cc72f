[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "finsight-ai"
version = "0.1.0"
description = "LLM-powered knowledge base for financial insights"
authors = [
    {name = "FinSight AI Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "python-dotenv>=1.0.0",
    "openai>=1.3.0",
    "langchain>=0.0.350",
    "langchain-openai>=0.0.2",
    "langchain-community>=0.0.10",
    "pypdf2>=3.0.1",
    "pandas>=2.1.4",
    "chromadb>=0.4.18",
    "tiktoken>=0.5.2",
    "requests>=2.31.0",
    "aiofiles>=23.2.0",
    "structlog>=23.2.0",
    "numpy>=1.25.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "black>=23.11.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
]
evaluation = [
    "ragas>=0.1.0",
    "datasets>=2.15.0",
]
cloud = [
    "azure-storage-blob>=12.19.0",
    "boto3>=1.34.0",
    "pinecone-client>=2.2.4",
]

[project.urls]
Homepage = "https://github.com/your-org/finsight-ai"
Repository = "https://github.com/your-org/finsight-ai"
Issues = "https://github.com/your-org/finsight-ai/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
