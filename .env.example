# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_EMBEDDING_MODEL=text-embedding-3-large

# Vector Database Configuration
VECTOR_DB_TYPE=chroma  # Options: chroma, pinecone, weaviate
CHROMA_PERSIST_DIRECTORY=./data/chroma_db
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=finsight-financial-kb

# Storage Configuration
BLOB_STORAGE_TYPE=local  # Options: local, azure, aws
AZURE_STORAGE_CONNECTION_STRING=your_azure_connection_string
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_BUCKET_NAME=finsight-documents

# Application Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true
LOG_LEVEL=INFO

# Chunking Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_CHUNKS_PER_QUERY=10

# Financial Data Configuration
TARGET_COMPANIES=castrol,veedol,valvoline
SUPPORTED_YEARS=2020,2021,2022,2023,2024

# Evaluation Configuration
ENABLE_RAGAS_EVALUATION=true
FEEDBACK_STORAGE_PATH=./data/feedback

# Compliance Configuration
INCLUDE_DISCLAIMERS=true
DISCLAIMER_TEXT="This information is for educational purposes only and should not be considered as financial advice."
